<script setup>
import { generateChartConfig } from '@sensehawk/chart-generator';
import { watchDebounced } from '@vueuse/core';
import * as echarts from 'echarts';
import { storeToRefs } from 'pinia';
import { nextTick, onBeforeUnmount } from 'vue';
import { BI_CHART_COLOR_PALETTES } from '~/bi/constants/bi-constants';
import { useBiStore } from '~/bi/store/bi.store';
import BiBottomDrawer from './bi-bottom-drawer.vue';

const emit = defineEmits(['continue']);

const bi_store = useBiStore();
const { chart_builder_config, chart_builder_data, are_chart_builder_fields_filled } = storeToRefs(bi_store);

const state = reactive({
  chart_instance: false,
  echarts_config: null,
});

async function renderWidget() {
  if (chart_builder_config.value.chart_type === 'table') {
    // Probably some data processing
  }
  else {
    // TODO: 'funnel', 'pyramid', 'gauge' support will be added soon. Gauge will likely have a fcConfig
    const config = {
      type: chart_builder_config.value.chart_type.replace('_chart', ''),
    };

    // Layout tab
    const values = Array.isArray(chart_builder_config.value.layout_values)
      ? chart_builder_config.value.layout_values
      : [{ value: chart_builder_config.value.layout_values }];
    if (!are_chart_builder_fields_filled.value) {
      state.chart_instance?.dispose?.();
      return;
    }
    await nextTick();
    config.data = {
      category: chart_builder_config.value.layout_category,
      values: values.map(item => item.value),
      stackBy: chart_builder_config.value.stack_by === 'none' ? null : chart_builder_config.value.stack_by,
    };

    // Series config
    if (chart_builder_config.value.layout_values && ['column_chart', 'horizontalBar_chart', 'line_chart', 'area_chart', 'mixed_chart', 'scatter_chart'].includes(chart_builder_config.value.chart_type)) {
      config.series = {
        ...Object.values(chart_builder_config.value.layout_values).reduce((acc, item) => {
          acc[item.value] = {
            name: item.legend || item.value,
            type: item.chart_type,
            yAxisIndex: item.y_axis === 'primary' ? 0 : 1,
            color: item.color,
            ...(!item.stack ? { stack: false } : {}),
            lineColor: item.color,
            lineWidth: Number.parseInt(item.line_width),
            lineStyle: item.line_style,
            smooth: item.line_shape === 'curved',
            prefix: item.prefix,
            suffix: item.suffix,
          };
          return acc;
        }, {}),
      };
    }

    //  Display tab
    config.layout = {
      title: chart_builder_config.value.title,
      subtitle: chart_builder_config.value.subtitle,
    };
    config.legend = {
      show: chart_builder_config.value.legend_position !== 'none',
      position: chart_builder_config.value.legend_position,
    };
    config.dataValues = {
      show: chart_builder_config.value.values === 'show',
      compact: chart_builder_config.value.compact,
      precision: chart_builder_config.value.precision,
      // TODO: Global prefix and suffix?
    };
    config.styling = {
      colors: BI_CHART_COLOR_PALETTES[chart_builder_config.value.color_palette]?.colors,
    };
    config.processing = {
      aggregation: chart_builder_config.value.aggregation,
    };

    // Axes tab
    config.axes = {
      categoryName: chart_builder_config.value.category_axis_name,
      valueName: chart_builder_config.value.value_axis_name,
      secondaryValueName: chart_builder_config.value.secondary_y_axis,
      categoryLabels: chart_builder_config.value.category_tick_label,
      valueLabels: chart_builder_config.value.value_tick_label,
      secondaryValueLabels: chart_builder_config.value.secondary_value_tick_label,
      ...(!Number.isNaN(Number.parseInt(chart_builder_config.value.custom_range_min)) ? { valueMin: Number.parseInt(chart_builder_config.value.custom_range_min) } : {}),
      ...(!Number.isNaN(Number.parseInt(chart_builder_config.value.custom_range_max)) ? { valueMax: Number.parseInt(chart_builder_config.value.custom_range_max) } : {}),
      ...(!Number.isNaN(Number.parseInt(chart_builder_config.value.secondary_value_min)) ? { secondaryValueMin: Number.parseInt(chart_builder_config.value.secondary_value_min) } : {}),
      ...(!Number.isNaN(Number.parseInt(chart_builder_config.value.secondary_value_max)) ? { secondaryValueMax: Number.parseInt(chart_builder_config.value.secondary_value_max) } : {}),
      valueScale: chart_builder_config.value.primary_scale,
      secondaryValueScale: chart_builder_config.value.secondary_scale,
    };

    // // Advanced tab
    config.accessibility = {
      enabled: chart_builder_config.value.accessibility_patterns,
      // TODO: New option?
      decalPatterns: false,
    };
    config.interactions = {
      // TODO: Not working as expected
      dataZoom: {
        enabled: chart_builder_config.value.data_zoom !== 'disabled',
        type: chart_builder_config.value.data_zoom,
      },
      // TODO: tooltip, brush?
    };
    // TODO: Reference lines?
    // if (chart_builder_config.value.reference_lines) {
    //   helper.setReferenceLines(chart_builder_config.value.reference_lines.map(line => ({
    //     value: line.value,
    //     label: line.label,
    //     color: line.color,
    //     lineStyle: line.line_style,
    //   })).filter(line => line.value));
    // }

    if (chart_builder_config.value.chart_type === 'pareto_chart') {
      config.chartSpecific = {
        pareto: {
          show80PercentLine: chart_builder_config.value.show_eighty_percent_line,
          eightyPercentLineColor: chart_builder_config.value.eighty_percent_line_color,
          eightyPercentLineStyle: chart_builder_config.value.eighty_percent_line_style,
          barColor: chart_builder_config.value.bar_color,
          lineColor: chart_builder_config.value.cumulative_line_color,
        },
      };
    }
    else if (chart_builder_config.value.chart_type === 'heatmap_chart') {
      config.chartSpecific = {
        heatmap: {
          colorScheme: BI_CHART_COLOR_PALETTES[chart_builder_config.value.color_scheme]?.colors,
          colorType: chart_builder_config.value.color_type,
          showColorScale: chart_builder_config.value.color_scale,
          colorScalePosition: chart_builder_config.value.color_scale_position,
        },
      };
    }
    else if (chart_builder_config.value.chart_type === 'waterfall_chart') {
      config.chartSpecific = {
        waterfall: {
          showSum: chart_builder_config.value.show_sum,
          positiveColor: chart_builder_config.value.positive_color,
          negativeColor: chart_builder_config.value.negative_color,
          sumColor: chart_builder_config.value.sum_color,
        },
      };
    }

    const echarts_config = generateChartConfig(chart_builder_data.value, config);
    const chartElement = document.getElementById('chart-container');
    if (!state.chart_instance) {
      state.chart_instance = echarts.init(chartElement);
    }
    else if (state.chart_instance) {
      state.chart_instance.dispose();
      state.chart_instance = echarts.init(chartElement);
    }
    state.chart_instance.setOption(echarts_config);
  }
}

function downloadWidget() {
  const imgData = state.chart_instance.getDataURL({
    type: 'png',
    pixelRatio: 2,
    backgroundColor: '#fff',
  });
  const a = document.createElement('a');
  a.href = imgData;
  a.download = 'chart.png';
  a.click();
}

watchDebounced(
  () => chart_builder_config.value,
  async () => {
    await nextTick();
    renderWidget();
  },
  { deep: true, immediate: true, debounce: 300 },
);

onBeforeUnmount(() => {
  if (state.chart_instance) {
    state.chart_instance.dispose();
    state.chart_instance = null;
  }
});
</script>

<template>
  <div class="h-full w-full flex flex-col relative">
    <div class="p-6 flex-1">
      <div v-if="!are_chart_builder_fields_filled" class="h-full w-full flex flex-col justify-center items-center text-center">
        <IconIllustrationBiEmptyChartBuilder />
        <div class="text-sm font-semibold text-gray-900 mt-4 mb-1">
          No chart to show
        </div>
        <div class="text-sm font-normal text-gray-600">
          Configure the chart from left panel to start generating the chart
        </div>
      </div>
      <div v-else-if="chart_builder_config.chart_type === 'table'">
        TABLE
      </div>
      <div v-else id="chart-container" class="h-full w-full" />
    </div>

    <hr>
    <div class="p-6 w-full flex justify-end items-center">
      <HawkButton type="outlined" class="mr-3" @click="downloadWidget">
        <IconHawkDownloadOne />
        Download (temporary)
      </HawkButton>
      <HawkButton @click="emit('continue')">
        <IconHawkRocketTwo />
        Publish widget
      </HawkButton>
    </div>

    <BiBottomDrawer
      show-text="Show results"
      hide-text="Hide results"
    >
      <template #default>
        TABLE
      </template>
    </BiBottomDrawer>
  </div>
</template>
